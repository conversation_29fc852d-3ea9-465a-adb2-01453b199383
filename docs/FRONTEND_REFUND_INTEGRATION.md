# Frontend Integration Guide - Refund Transaction APIs

## Overview

Hướng dẫn tích hợp 2 API mới để xử lý refund transaction trong admin order detail:

1. **GET** `/admin/v1/orders/:id/refund-status` - <PERSON><PERSON><PERSON> tra khả năng refund
2. **POST** `/admin/v1/orders/:id/refund-transaction` - Thực hiện refund

## API Endpoints

### 1. Kiểm tra Refund Status

**Endpoint:** `GET /admin/v1/orders/:id/refund-status`

**Headers:**

```typescript
{
  'Authorization': `Bearer ${adminToken}`,
  'Content-Type': 'application/json'
}
```

**Response:**

```typescript
interface RefundStatusResponse {
  success: boolean
  data: {
    orderId: string
    orderName: string
    orderStatus: string
    financialStatus: string
    cancelledAt: string | null
    transaction: {
      id: string
      status: string
      amount: number
      currency: string
      source: string
      sourceId: string
      createdAt: string
    } | null
    canRefund: boolean
    refundReason: string | null
  }
}
```

**Ví dụ Response:**

```json
{
  "success": true,
  "data": {
    "orderId": "123e4567-e89b-12d3-a456-426614174000",
    "orderName": "Order #1001",
    "orderStatus": "active",
    "financialStatus": "paid",
    "cancelledAt": null,
    "transaction": {
      "id": "456e7890-e89b-12d3-a456-426614174000",
      "status": "completed",
      "amount": 99.99,
      "currency": "USD",
      "source": "authorize_net",
      "sourceId": "auth-123",
      "createdAt": "2024-01-01T00:00:00.000Z"
    },
    "canRefund": true,
    "refundReason": null
  }
}
```

### 2. Thực hiện Refund

**Endpoint:** `POST /admin/v1/orders/:id/refund-transaction`

**Headers:**

```typescript
{
  'Authorization': `Bearer ${adminToken}`,
  'Content-Type': 'application/json'
}
```

**Request Body:** Không cần body (đã được tinh chỉnh)

**Response:** Trả về trực tiếp kết quả từ TransactionService

**Ví dụ Response:**

```json
{
  "success": true,
  "transactionId": "ref-123",
  "amount": 99.99
}
```

## Frontend Integration

### 1. Tạo Service Functions

```typescript
// services/orderRefundService.ts

interface RefundStatusData {
  orderId: string
  orderName: string
  orderStatus: string
  financialStatus: string
  cancelledAt: string | null
  transaction: {
    id: string
    status: string
    amount: number
    currency: string
    source: string
    sourceId: string
    createdAt: string
  } | null
  canRefund: boolean
  refundReason: string | null
}

export class OrderRefundService {
  private baseUrl = '/admin/v1/orders'
  private token: string

  constructor(token: string) {
    this.token = token
  }

  private getHeaders() {
    return {
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json',
    }
  }

  /**
   * Kiểm tra khả năng refund của order
   */
  async getRefundStatus(orderId: string): Promise<RefundStatusData> {
    const response = await fetch(`${this.baseUrl}/${orderId}/refund-status`, {
      method: 'GET',
      headers: this.getHeaders(),
    })

    if (!response.ok) {
      throw new Error(`Failed to get refund status: ${response.statusText}`)
    }

    const result = await response.json()
    return result.data
  }

  /**
   * Thực hiện refund transaction
   */
  async refundTransaction(orderId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/${orderId}/refund-transaction`, {
      method: 'POST',
      headers: this.getHeaders(),
    })

    if (!response.ok) {
      throw new Error(`Failed to refund transaction: ${response.statusText}`)
    }

    return await response.json()
  }
}
```

### 2. React Hook cho Refund

```typescript
// hooks/useOrderRefund.ts

import { useState, useCallback } from 'react'
import { OrderRefundService } from '../services/orderRefundService'

interface UseOrderRefundProps {
  orderId: string
  token: string
}

export const useOrderRefund = ({ orderId, token }: UseOrderRefundProps) => {
  const [refundStatus, setRefundStatus] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const orderRefundService = new OrderRefundService(token)

  const checkRefundStatus = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const status = await orderRefundService.getRefundStatus(orderId)
      setRefundStatus(status)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check refund status')
    } finally {
      setLoading(false)
    }
  }, [orderId, orderRefundService])

  const performRefund = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await orderRefundService.refundTransaction(orderId)

      // Refresh refund status sau khi refund
      await checkRefundStatus()

      return result
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refund transaction')
      throw err
    } finally {
      setLoading(false)
    }
  }, [orderId, orderRefundService, checkRefundStatus])

  return {
    refundStatus,
    loading,
    error,
    checkRefundStatus,
    performRefund,
  }
}
```

### 3. React Component cho Refund Button

```typescript
// components/RefundButton.tsx

import React, { useEffect, useState } from 'react';
import { useOrderRefund } from '../hooks/useOrderRefund';

interface RefundButtonProps {
  orderId: string;
  adminToken: string;
  onRefundSuccess?: () => void;
}

export const RefundButton: React.FC<RefundButtonProps> = ({
  orderId,
  adminToken,
  onRefundSuccess
}) => {
  const { refundStatus, loading, error, checkRefundStatus, performRefund } = useOrderRefund({
    orderId,
    token: adminToken
  });

  const [showConfirmModal, setShowConfirmModal] = useState(false);

  useEffect(() => {
    checkRefundStatus();
  }, [orderId, checkRefundStatus]);

  const handleRefundClick = () => {
    setShowConfirmModal(true);
  };

  const handleConfirmRefund = async () => {
    try {
      await performRefund();
      setShowConfirmModal(false);
      onRefundSuccess?.();
    } catch (err) {
      console.error('Refund failed:', err);
    }
  };

  // Không hiển thị button nếu không thể refund
  if (!refundStatus?.canRefund) {
    return null;
  }

  return (
    <>
      <button
        onClick={handleRefundClick}
        disabled={loading}
        className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded disabled:opacity-50"
      >
        {loading ? 'Processing...' : 'Refund Transaction'}
      </button>

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Confirm Refund</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to refund this transaction? This action cannot be undone.
            </p>

            {refundStatus?.transaction && (
              <div className="bg-gray-50 p-3 rounded mb-4">
                <p><strong>Amount:</strong> ${refundStatus.transaction.amount}</p>
                <p><strong>Transaction ID:</strong> {refundStatus.transaction.sourceId}</p>
              </div>
            )}

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConfirmModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmRefund}
                disabled={loading}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                {loading ? 'Processing...' : 'Confirm Refund'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="mt-2 text-red-600 text-sm">
          {error}
        </div>
      )}
    </>
  );
};
```

### 4. Sử dụng trong Order Detail

```typescript
// pages/OrderDetail.tsx

import React from 'react';
import { RefundButton } from '../components/RefundButton';

interface OrderDetailProps {
  order: any;
  adminToken: string;
}

export const OrderDetail: React.FC<OrderDetailProps> = ({ order, adminToken }) => {
  const handleRefundSuccess = () => {
    // Refresh order data hoặc hiển thị thông báo
    console.log('Refund completed successfully');
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Order #{order.name}</h1>

        {/* Refund Button */}
        <RefundButton
          orderId={order.id}
          adminToken={adminToken}
          onRefundSuccess={handleRefundSuccess}
        />
      </div>

      {/* Order details... */}
    </div>
  );
};
```

## Business Logic

### Điều kiện hiển thị nút Refund:

```typescript
const shouldShowRefundButton = (refundStatus: any) => {
  return refundStatus?.canRefund === true
}
```

### Các trường hợp không hiển thị nút:

1. **Order không có transaction** → `refundReason: "Order has no transaction"`
2. **Transaction đã được refund** → `refundReason: "Transaction has already been refunded"`
3. **Transaction status không hợp lệ** → `refundReason: "Transaction status 'pending' is not refundable"`
4. **Order đã bị cancelled** → `refundReason: "Order is already cancelled"`

## Error Handling

### Common Error Scenarios:

```typescript
try {
  await performRefund()
} catch (error) {
  if (error.message.includes('Order has no transaction')) {
    // Hiển thị thông báo: Order này không có transaction để refund
  } else if (error.message.includes('already been refunded')) {
    // Hiển thị thông báo: Transaction đã được refund
  } else if (error.message.includes('not refundable')) {
    // Hiển thị thông báo: Transaction status không cho phép refund
  } else {
    // Hiển thị thông báo lỗi chung
  }
}
```

## State Management

### Cập nhật UI sau khi refund:

```typescript
const handleRefundSuccess = async () => {
  // 1. Refresh refund status
  await checkRefundStatus()

  // 2. Refresh order data
  await refreshOrderData()

  // 3. Hiển thị thông báo thành công
  showSuccessMessage('Transaction refunded successfully')

  // 4. Ẩn nút refund (vì đã refund xong)
  // Button sẽ tự động ẩn vì canRefund = false
}
```

## Testing

### Test Cases cho Frontend:

1. **Button hiển thị đúng** khi order có thể refund
2. **Button ẩn** khi order không thể refund
3. **Loading state** hiển thị đúng khi đang xử lý
4. **Error handling** hiển thị thông báo lỗi phù hợp
5. **Success flow** cập nhật UI sau khi refund thành công
6. **Modal confirmation** hoạt động đúng

## Notes

- API không cần request body (đã được tinh chỉnh)
- Response format đơn giản hơn, trả về trực tiếp kết quả từ service
- Frontend cần handle loading state và error state
- Nên có confirmation modal trước khi thực hiện refund
- Refresh data sau khi refund thành công
