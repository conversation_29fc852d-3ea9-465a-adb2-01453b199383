/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import { middleware } from '#start/kernel'

const AdminOrderController = () => import('#adminControllers/order/order_controller')

export default function adminOrderRoutes() {
  router.group(() => {
    router.post('order/check-unfulfill', [AdminOrderController, 'checkUnfulfill'])
  })

  router
    .group(() => {
      router.resource('order', AdminOrderController)
      router.put('order/:id/sync', [AdminOrderController, 'sync'])
      router.get('order/:id/refund-status', [AdminOrderController, 'getRefundStatus'])
      router.post('order/:id/refund-transaction', [AdminOrderController, 'refundOrderTransaction'])
    })
    .use(middleware.auth({ guards: ['jwt_admin'] as any }))
}
