import ZnOrder from '#models/zn_order'
import type { HttpContext } from '@adonisjs/core/http'
// import { createOrderValidator } from '../../validators/order/order_validator.js'
import CheckUnfulfillOrdersJob from '#jobs/check_unfulfill_orders_job'
import Zn<PERSON>ddress from '#models/zn_address'
import { OrderService } from '#services/shop/order_service'
import { TransactionService } from '#services/transaction_service'
import queue from '@rlanz/bull-queue/services/main'
import { ACTION, RESOURCE } from '../../../app/constants/authorization.js'
import {
  createOrderValidator,
  updateOrderValidator,
  refundTransactionValidator,
} from '../../validators/order/order_validator.js'
import { TransactionStatus } from '#constants/transaction'

export default class AdminOrderController {
  private orderService = new OrderService()
  private transactionService = new TransactionService()

  /**
   * @index
   * @tag Admin Order
   * @summary Read all orders
   * @paramQuery page - Page Number (default 1) - @type(number)
   * @paramQuery limit - Page Limit (default 10) - @type(number)
   * @responseBody 200 - <ZnOrder[]>.append("id":"").paginated() - Read all orders descriptively
   */
  /**
   * Display a list of resource
   */
  async index({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ORDER)

    try {
      const { page = 1, limit = 10, search } = request.qs()

      const query = ZnOrder.query()
        // .withCount('orderDetails')
        .preload('orderDetails', (detailsQuery) => {
          detailsQuery.preload('variant', (variantQuery) => {
            variantQuery.preload('image').preload('product')
          })
        })
        .preload('user')

      if (search) {
        query.where((queryBuilder) => {
          queryBuilder.whereRaw(`LOWER(name) LIKE LOWER(?)`, [`%${search}%`])
        })
      }

      query.orderBy('createdAt', 'desc')

      const result = await query.paginate(page, limit)

      return response.ok(result)
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @create
   * @tag Admin Order
   * @summary Return info for creation
   * @responseBody 200 - {"info":{"permissions":["ZnPermission"]}} - Return info for creation descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   */
  /**
   * Display form to create a new record
   */
  async create({ bouncer, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.ORDER)

    try {
      return response.ok({
        info: {},
      })
    } catch (error) {
      return response.badRequest(error)
    }
  }

  /**
   * @store
   * @tag Admin Order
   * @summary Create a order
   * @requestBody <ZnOrder>.append("permissionIds":[""])
   * @responseBody 201 - <ZnOrder>.append("id":"","createdAt":"","updatedAt":"")
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]}
   * @responseBody 401 - Unauthorized access
   */
  /**
   * Handle form submission for the create action
   */
  async store({ bouncer, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.CREATE, RESOURCE.ORDER)

    const data = request.all()

    const payload = await createOrderValidator.validate(data)
    payload

    try {
      // const created = await ZnOrder.create({
      //     // name: payload.name,
      // })
      //   if (payload.permissionIds && payload.permissionIds.length > 0) {
      //     await created.related('permissions').sync(payload.permissionIds)
      //   }
      // return response.created(created)
    } catch (error) {
      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @show
   * @tag Admin Order
   * @summary Read order
   * @paramPath id - ID of Order - @type(string) @required
   * @responseBody 200 - <ZnOrder>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - Read a order descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Order not found"} - Not Found
   */
  /**
   * Show individual record
   */
  async show({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.READ, RESOURCE.ORDER)

    const orderId = params.id

    const order = await ZnOrder.query()
      .where('id', orderId)
      .preload('orderDetails', (orderDetailQuery) => {
        orderDetailQuery.preload('variant', (variantQuery) => {
          variantQuery.preload('image').preload('product', (productQuery) => {
            productQuery.preload('vendor')
          })
        })
      })
      // .withCount('orderDetails')
      .preload('user')
      .preload('shipping')
      .preload('billing')
      .preload('fulfillments', (fulfillmentQuery) => {
        fulfillmentQuery
          .where({ status: 'success' })
          .preload('orderDetails', (orderDetailQuery) => {
            orderDetailQuery.preload('variant', (variantQuery) => {
              variantQuery.preload('image').preload('product', (productQuery) => {
                productQuery.preload('vendor')
              })
            })
          })
      })
      .first()

    if (!order) {
      return response.notFound({ message: 'Order not found' })
    }

    return response.ok(order)
  }

  /**
   * @edit
   * @tag Admin Order
   * @summary Return info for updating
   * @responseBody 200 - {"data":"<ZnOrder>","permissions":{"orders":["ZnPermission"]}} - Return info for updating descriptively
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Order not found"} - Not Found
   */
  /**
   * Edit individual record
   */
  async edit({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ORDER)

    try {
      const orderId = params.id

      const order = await ZnOrder.query()
        .where('id', orderId)
        .preload('orderDetails', (orderDetailQuery) => {
          orderDetailQuery.preload('variant', (variantQuery) => {
            variantQuery
              .preload('image')
              .preload('product')
              .preload('optionValues', (optionValueQuery) => {
                optionValueQuery.preload('option')
              })
          })
        })
        // .withCount('orderDetails', (orderDetailQuery) => orderDetailQuery.sum('quantity'))
        .preload('user')
        .preload('billing')
        .preload('shipping')
        .first()

      if (!order) {
        return response.notFound({ message: 'Order not found' })
      }

      return response.ok({
        data: {
          ...order?.serialize(),
        },
        info: {},
      })
    } catch (error) {
      console.log(error)

      return response.badRequest(error)
    }
  }

  /**
   * @update
   * @tag Admin Order
   * @summary Update order
   * @description Update order descriptively
   * @paramPath id - ID of Order - @type(string) @required
   * @requestBody <ZnOrder>.append("permissionIds":[""])
   * @responseBody 200 - <ZnOrder>.append("id":"","createdAt":"","updatedAt":"","deletedAt":null) - OK
   * @responseBody 400 - {"message":"Validation failed","errors":[{"message":"The name field must be defined","rule":"required","field":"name"}]} - Bad Request
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Order not found"} - Not Found
   */
  /**
   * Handle form submission for the edit action
   */
  async update({ bouncer, params, request, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ORDER)

    const orderId = params.id

    const order = await ZnOrder.query().where('id', orderId).first()

    if (!order) {
      return response.notFound({ message: 'Order not found' })
    }

    const data = request.all()

    const payload = await updateOrderValidator.validate(data)

    try {
      order.name = payload.name
      order.email = payload.email
      order.userId = payload.userId || null

      if (payload.billingId) {
        order.billingId = payload.billingId
      } else if (payload.billing) {
        const billing = await ZnAddress.create({
          ...payload.billing,
        })
        order.billingId = billing.id
      }

      if (payload.sameBillShip) {
        order.shippingId = order.billingId
      } else if (payload.shippingId) {
        order.shippingId = payload.shippingId
      } else if (payload.shipping) {
        const shipping = await ZnAddress.create({
          ...payload.shipping,
        })
        order.shippingId = shipping.id
      }

      order.fulfillmentStatus = payload.fulfillmentStatus
      order.financialStatus = payload.financialStatus

      const updated = await order.save()

      return response.ok(updated)
    } catch (error) {
      console.log(error)

      return response.internalServerError({
        message: 'Something went wrong',
      })
    }
  }

  /**
   * @destroy
   * @tag Admin Order
   * @summary Soft-delete a order
   * @description Soft-delete a order descriptively
   * @paramPath id - ID of Order - @type(string) @required
   * @responseBody 200 - {"message":"Order soft-deleted successfully"} - OK
   * @responseBody 401 - Unauthorized access - Unauthorized
   * @responseBody 404 - {"message":"Order not found"} - Not Found
   */
  /**
   * Delete record
   */
  async destroy({ bouncer, params, response }: HttpContext) {
    await bouncer.authorize('allow', ACTION.DELETE, RESOURCE.ORDER)

    const orderId = params.id

    const order = await ZnOrder.find(orderId)

    if (!order) {
      return response.notFound({ message: 'Order not found' })
    }

    await order.softDelete()

    return response.ok({ message: 'Order soft-deleted successfully' })
  }

  /**
   * @checkUnfulfill
   * @tag Admin Order
   * @summary Trigger check unfulfill orders job
   * @responseBody 200 - Job dispatched successfully
   */
  async checkUnfulfill({ response }: HttpContext) {
    try {
      await queue.dispatch(CheckUnfulfillOrdersJob, { days: 7 })

      return response.ok({
        message: 'Send unfulfill orders notification dispatched successfully',
      })
    } catch (error) {
      console.error('Error dispatching check unfulfill orders job:', error)
      return response.internalServerError({
        message: 'Failed to dispatch check unfulfill orders job',
        error: error.message,
      })
    }
  }

  /**
   * @sync
   * @tag Admin Order
   * @summary Sync Order from Shopify
   * @description Sync Order from Shopify descriptively
   * @paramPath id - Id of Order - @type(string) @required
   * @responseBody 200 - Job dispatched successfully
   */
  async sync({ params, request, response }: HttpContext) {
    try {
      const orderId = params.id

      const { async } = request.body()

      if (async) {
        await this.orderService.asyncOrder(orderId)

        return response.ok('Sync Order Job Sent!')
      } else {
        await this.orderService.syncOrder(orderId)

        return response.ok('Order Synced!')
      }
    } catch (error) {
      console.error(error)
      return response.internalServerError({
        message: 'Something went wrong.',
        error: error,
      })
    }
  }

  async refundOrderTransaction({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.UPDATE, RESOURCE.ORDER)

      const orderId = params.id

      const order = await ZnOrder.query()
        .where('id', orderId)
        .preload('transaction', (query) => {
          query.whereIn('status', [TransactionStatus.COMPLETED, TransactionStatus.CAPTURED])
        })
        .preload('user')
        .firstOrFail()

      const canRefund = this.canOrderBeRefunded(order)
      if (!canRefund.canRefund) {
        return response.badRequest({
          success: false,
          message: canRefund.reason,
        })
      }

      const result = await this.transactionService.processRefund(order.transaction!.id)

      return response.ok(result)
    } catch (error) {
      console.error('Error in refundOrderTransaction:', error)
      if (error.status == 500) return response.internalServerError(error)
      else return response.badRequest(error)
    }
  }

  async getRefundStatus({ bouncer, params, response }: HttpContext) {
    try {
      await bouncer.authorize('allow', ACTION.READ, RESOURCE.ORDER)

      const orderId = params.id

      const order = await ZnOrder.query().where('id', orderId).preload('transaction').firstOrFail()

      const canRefund = this.canOrderBeRefunded(order)

      return response.ok({
        success: true,
        data: {
          orderId: order.id,
          orderName: order.name,
          orderStatus: order.status,
          financialStatus: order.financialStatus,
          cancelledAt: order.cancelledAt,
          transaction: order.transaction
            ? {
                id: order.transaction.id,
                status: order.transaction.status,
                amount: order.transaction.amount,
                currency: order.transaction.currency,
                source: order.transaction.source,
                sourceId: order.transaction.sourceId,
                createdAt: order.transaction.createdAt,
              }
            : null,
          canRefund: canRefund.canRefund,
          refundReason: canRefund.reason,
        },
      })
    } catch (error) {
      console.error('Error in getRefundStatus:', error)
      if (error.status == 500) return response.internalServerError(error)
      else return response.badRequest(error)
    }
  }

  private canOrderBeRefunded(order: ZnOrder): { canRefund: boolean; reason?: string } {
    if (!order.transaction) {
      return {
        canRefund: false,
        reason: 'Order has no transaction',
      }
    }

    if (order.transaction.status === TransactionStatus.REFUNDED) {
      return {
        canRefund: false,
        reason: 'Transaction has already been refunded',
      }
    }

    if (
      ![TransactionStatus.COMPLETED, TransactionStatus.CAPTURED].includes(
        order.transaction.status as TransactionStatus
      )
    ) {
      return {
        canRefund: false,
        reason: `Transaction status '${order.transaction.status}' is not refundable`,
      }
    }

    if (order.cancelledAt) {
      return {
        canRefund: false,
        reason: 'Order is already cancelled',
      }
    }

    return { canRefund: true }
  }
}
