import { ECommissionGroupType } from "#constants/commission_group_type";
import { NOTIFICATION_TYPE } from "#constants/notification";
import TierChangedNotification from "#mails/affiliation/tier_changed_notification";
import AppMail from "#mails/app_mail";
import Notification from "#models/notification";
import ZnAffiliate from "#models/zn_affiliate";
import ZnUser from "#models/zn_user";
import { NotificationService } from "#services/notification_service";
import logger from "@adonisjs/core/services/logger";
import mail from "@adonisjs/mail/services/main";
import env from '#start/env';
import { AdminNotificationService } from "../../../admin/services/notification/admin_notification_service.js";
import { ACTION, RESOURCE } from "#constants/authorization";

export default class AffiliationNotificationService {
  private adminNotificationService: AdminNotificationService;

  constructor() {
    this.adminNotificationService = new AdminNotificationService();
  }

  async sendTierChangedNotification(affiliate: ZnAffiliate, isUpgrading: boolean) {
    await affiliate.load('affiliateTier', (query) => {
      query
        .preload('commissionGroups', (query: any) => {
          query
            .where('type', ECommissionGroupType.DEFAULT)
            .preload('commissionRates');
        })
    });

    if (!affiliate.affiliateTier ||
      affiliate.affiliateTier.commissionGroups.length === 0 ||
      affiliate.affiliateTier.commissionGroups[0].commissionRates.length === 0) {
      logger.error(`Cannot send tier changed email to ${affiliate.user.email}. The tier is not fully configured.`);
      return;
    }

    if (env.get('DISABLE_AFFILIATE_NOTIFICATIONS') === 'true') {
      const admins = await this.adminNotificationService.getAdminsByPermissions([
        { action: ACTION.READ, resource: RESOURCE.AFFILIATION },
      ])
      for (const admin of admins) {
        await this.sendEmail(
          new TierChangedNotification(
            affiliate.user.firstName,
            isUpgrading,
            affiliate.affiliateTier.commissionGroups[0].commissionRates,
            affiliate.user.email
          ),
          admin.username
        );
      }
    } else {
      await this.sendEmail(
        new TierChangedNotification(
          affiliate.user.firstName,
          isUpgrading,
          affiliate.affiliateTier.commissionGroups[0].commissionRates,
          affiliate.user.email
        ),
        affiliate.user.email
      );

      await this.sendPushNotification(
        affiliate.user,
        affiliate.id,
        isUpgrading ? 'Tier Upgraded!' : 'Tier Changed',
        isUpgrading
          ? 'Congratulations! Your affiliate tier has been upgraded. Check your new commission rates.'
          : 'Your affiliate tier has been downgraded. Review your updated commission rates in your dashboard.'
      )
    }
  }

  private async sendEmail(emailHandler: AppMail, userEmail: string) {
    await mail
      .send(emailHandler)
      .then(() => {
        logger.info(`${Object.getPrototypeOf(emailHandler).constructor.name} has been sent successfully to ${userEmail}`);
      })
      .catch((error) => {
        console.error('Error when sending email', error);
      })
  }

  private async sendPushNotification(
    user: ZnUser,
    resourceId: string,
    title: string,
    description: string
  ) {
    const notification = await Notification.create({
      type: NOTIFICATION_TYPE.AFFILIATE,
      userId: user.id,
      resourceId: resourceId,
      title: title,
      description: description,
    })

    if (notification) {
      const notificationService = new NotificationService()
      await notificationService.send([user], [notification])
    }
  }
}